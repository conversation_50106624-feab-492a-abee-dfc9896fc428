{"code": 0, "data": {"hname": "8月大酒店", "startDate": "2025-07-01", "endDate": "2025-07-30", "lastSelectTime": "2025-07-31 09:47:49", "operator": "", "list": [{"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1944676483861422081", "url": "/#/front/order/order_details?fullscreen=true&no=1944676483861422081&noType=order&modelValue=true&tabName=account", "togetherCode": "1944676483907559424", "name": "计费测试", "rtCode": "1812699025422946304", "rtName": "嗨致静谧大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-14 16:32:59", "checkOutTime": "2025-07-14 16:33:36", "payTime": "2025-07-14 16:33:36", "payBizDate": "2025-07-14", "payType": "房间账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 0.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 0.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "A103", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": null, "url": "", "togetherCode": "1", "name": "现付账-会员卡", "rtCode": null, "rtName": "", "inType": null, "inTypeName": null, "checkInTime": null, "checkOutTime": null, "payTime": null, "payBizDate": "2025-07-29", "payType": "现付账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 39.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 39.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 39.0, "payTotalFee": 39.0, "merge": 0.0, "payOperator": null, "payOperatorName": null, "payAccountList": null, "consumeAccountList": null, "rno": null, "gSrc": null, "gSrcName": null}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": null, "url": "", "togetherCode": "2", "name": "现付账-会员充值", "rtCode": null, "rtName": "", "inType": null, "inTypeName": null, "checkInTime": null, "checkOutTime": null, "payTime": null, "payBizDate": "2025-07-29", "payType": "现付账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 2346.0, "consumeTotalFee": 2346.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 2346.0, "payTotalFee": 2346.0, "merge": 0.0, "payOperator": null, "payOperatorName": null, "payAccountList": null, "consumeAccountList": null, "rno": null, "gSrc": null, "gSrcName": null}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1947463322389311489", "url": "/#/front/order/order_details?fullscreen=true&no=1947463322389311489&noType=order&modelValue=true&tabName=account", "togetherCode": "1947463322422865920", "name": "退房测试", "rtCode": "1820341977486307328", "rtName": "欢乐之家亲子双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-22 09:06:53", "checkOutTime": "2025-07-22 10:57:21", "payTime": "2025-07-22 10:57:21", "payBizDate": "2025-07-29", "payType": "房间账", "consSubFee": 444.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 444.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 444.0, "payTotalFee": 444.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "102", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1947196127625211905", "url": "/#/front/order/order_details?fullscreen=true&no=1947196127625211905&noType=order&modelValue=true&tabName=account", "togetherCode": "1947196127751041024", "name": "234111", "rtCode": "*********6720690176", "rtName": "沉浸式观影大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-21 15:25:09", "checkOutTime": "2025-07-22 10:44:33", "payTime": "2025-07-22 10:44:33", "payBizDate": "2025-07-29", "payType": "房间账", "consSubFee": 332.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 332.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 332.0, "payTotalFee": 332.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "B08", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": null, "url": "", "togetherCode": "3", "name": "现付账-小商品", "rtCode": null, "rtName": "", "inType": null, "inTypeName": null, "checkInTime": null, "checkOutTime": null, "payTime": null, "payBizDate": "2025-07-29", "payType": "现付账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 249.28, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 249.28, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 2.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 247.28, "payTotalFee": 249.28, "merge": 0.0, "payOperator": null, "payOperatorName": null, "payAccountList": null, "consumeAccountList": null, "rno": null, "gSrc": null, "gSrcName": null}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1935505742813249537", "url": "/#/front/order/order_details?fullscreen=true&no=1935505742813249537&noType=order&modelValue=true&tabName=account", "togetherCode": "1935505742855192576", "name": "问问", "rtCode": "1812733505999212544", "rtName": "商旅如意双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-06-19 09:11:44", "checkOutTime": null, "payTime": null, "payBizDate": "2025-07-29", "payType": "房间账", "consSubFee": 444.22, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 444.22, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 444.22, "payTotalFee": 444.22, "merge": 0.0, "payOperator": null, "payOperatorName": null, "payAccountList": null, "consumeAccountList": null, "rno": "LLL08", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1934853762239496192", "url": "/#/front/order/order_details?fullscreen=true&no=1934853762239496192&noType=order&modelValue=true&tabName=account", "togetherCode": "1934853762323382272", "name": "123", "rtCode": "*********6720690176", "rtName": "沉浸式观影大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-06-17 14:00:59", "checkOutTime": null, "payTime": null, "payBizDate": "2025-07-29", "payType": "房间账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 22.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 22.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 22.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 22.0, "merge": 0.0, "payOperator": null, "payOperatorName": null, "payAccountList": null, "consumeAccountList": null, "rno": "245", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1947534818310676481", "url": "/#/front/order/order_details?fullscreen=true&no=1947534818310676481&noType=order&modelValue=true&tabName=account", "togetherCode": "1947534818348425216", "name": "时租房", "rtCode": "*********6720690176", "rtName": "沉浸式观影大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-22 13:50:59", "checkOutTime": null, "payTime": null, "payBizDate": "2025-07-29", "payType": "房间账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 7.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 7.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 7.0, "payTotalFee": 7.0, "merge": 0.0, "payOperator": null, "payOperatorName": null, "payAccountList": null, "consumeAccountList": null, "rno": "B06", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1938445366124675072", "url": "/#/front/order/order_details?fullscreen=true&no=1938445366124675072&noType=order&modelValue=true&tabName=account", "togetherCode": "1938445536786710528", "name": "12", "rtCode": "186857*************", "rtName": "嗨致静谧双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-06-27 11:53:25", "checkOutTime": null, "payTime": null, "payBizDate": "2025-07-29", "payType": "房间账", "consSubFee": 222.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 222.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 222.0, "payTotalFee": 222.0, "merge": 0.0, "payOperator": null, "payOperatorName": null, "payAccountList": null, "consumeAccountList": null, "rno": "4539", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1933423063586398208", "url": "/#/front/order/order_details?fullscreen=true&no=1933423063586398208&noType=order&modelValue=true&tabName=account", "togetherCode": "1933423562305921024", "name": "同住客人 1", "rtCode": "1831598419748958208", "rtName": "悦致城景大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-06-13 15:17:53", "checkOutTime": null, "payTime": null, "payBizDate": "2025-07-28", "payType": "房间账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 4.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 4.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 4.0, "payTotalFee": 4.0, "merge": 0.0, "payOperator": null, "payOperatorName": null, "payAccountList": null, "consumeAccountList": null, "rno": "ADS08", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1934854595094052865", "url": "/#/front/order/order_details?fullscreen=true&no=1934854595094052865&noType=order&modelValue=true&tabName=account", "togetherCode": "1934854595123412992", "name": "98", "rtCode": "1820341977486307328", "rtName": "欢乐之家亲子双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-06-17 14:04:18", "checkOutTime": "2025-07-24 18:56:04", "payTime": "2025-07-24 18:56:04", "payBizDate": "2025-07-24", "payType": "房间账", "consSubFee": 16428.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 16428.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 16428.0, "payTotalFee": 16428.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "304", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1947534818310676481", "url": "/#/front/order/order_details?fullscreen=true&no=1947534818310676481&noType=order&modelValue=true&tabName=account", "togetherCode": "1947534818348425216", "name": "时租房", "rtCode": "*********6720690176", "rtName": "沉浸式观影大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-22 13:50:59", "checkOutTime": null, "payTime": null, "payBizDate": "2025-07-24", "payType": "房间账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 2.0, "consSubOth": 0.0, "consumeTotalFee": 2.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 2.0, "payTotalFee": 2.0, "merge": 0.0, "payOperator": null, "payOperatorName": null, "payAccountList": null, "consumeAccountList": null, "rno": "B06", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1947534818310676481", "url": "/#/front/order/order_details?fullscreen=true&no=1947534818310676481&noType=order&modelValue=true&tabName=account", "togetherCode": "1947534818348425216", "name": "时租房", "rtCode": "*********6720690176", "rtName": "沉浸式观影大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-22 13:50:59", "checkOutTime": null, "payTime": null, "payBizDate": "2025-07-23", "payType": "房间账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 0.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 0.0, "merge": 0.0, "payOperator": null, "payOperatorName": null, "payAccountList": null, "consumeAccountList": null, "rno": "B06", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1947463322389311489", "url": "/#/front/order/order_details?fullscreen=true&no=1947463322389311489&noType=order&modelValue=true&tabName=account", "togetherCode": "1947463322422865920", "name": "退房测试", "rtCode": "1820341977486307328", "rtName": "欢乐之家亲子双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-22 09:06:53", "checkOutTime": "2025-07-22 10:57:21", "payTime": "2025-07-22 10:57:21", "payBizDate": "2025-07-22", "payType": "房间账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 0.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 0.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "102", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1947196127625211905", "url": "/#/front/order/order_details?fullscreen=true&no=1947196127625211905&noType=order&modelValue=true&tabName=account", "togetherCode": "1947196127751041024", "name": "234111", "rtCode": "*********6720690176", "rtName": "沉浸式观影大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-21 15:25:09", "checkOutTime": "2025-07-22 10:44:33", "payTime": "2025-07-22 10:44:33", "payBizDate": "2025-07-22", "payType": "房间账", "consSubFee": 332.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 332.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 332.0, "payTotalFee": 332.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "B08", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1947196127625211905", "url": "/#/front/order/order_details?fullscreen=true&no=1947196127625211905&noType=order&modelValue=true&tabName=account", "togetherCode": "1947486287293902848", "name": "基金会", "rtCode": "*********6720690176", "rtName": "沉浸式观影大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-22 10:38:08", "checkOutTime": "2025-07-22 10:45:13", "payTime": "2025-07-22 10:45:13", "payBizDate": "2025-07-22", "payType": "房间账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 0.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 0.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "B08", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1947463322389311489", "url": "/#/front/order/order_details?fullscreen=true&no=1947463322389311489&noType=order&modelValue=true&tabName=account", "togetherCode": "1947463359303380993", "name": "次数", "rtCode": "1820341977486307328", "rtName": "欢乐之家亲子双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-22 09:07:02", "checkOutTime": "2025-07-22 10:57:21", "payTime": "2025-07-22 10:57:21", "payBizDate": "2025-07-22", "payType": "房间账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 0.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 0.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "102", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1947491380261347329", "url": "/#/front/order/order_details?fullscreen=true&no=1947491380261347329&noType=order&modelValue=true&tabName=account", "togetherCode": "1947491380332650496", "name": "1234234", "rtCode": "*********6720690176", "rtName": "沉浸式观影大床房", "inType": "hour_room", "inTypeName": "钟点房", "checkInTime": "2025-07-22 10:58:22", "checkOutTime": "2025-07-22 11:04:12", "payTime": "2025-07-22 11:04:12", "payBizDate": "2025-07-22", "payType": "房间账", "consSubFee": 29.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 29.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 29.0, "payTotalFee": 29.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "B01", "gSrc": "member", "gSrcName": "会员"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1945302659499872256", "url": "/#/front/order/order_details?fullscreen=true&no=1945302659499872256&noType=order&modelValue=true&tabName=account", "togetherCode": "1945302659634089984", "name": "实时框框", "rtCode": "1812733505999212544", "rtName": "商旅如意双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-16 09:01:11", "checkOutTime": "2025-07-22 11:04:29", "payTime": "2025-07-22 11:04:29", "payBizDate": "2025-07-22", "payType": "房间账", "consSubFee": 1312.66, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 1312.66, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 1312.66, "payTotalFee": 1312.66, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "uu03", "gSrc": "member", "gSrcName": "会员"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1944675661924638721", "url": "/#/front/order/order_details?fullscreen=true&no=1944675661924638721&noType=order&modelValue=true&tabName=account", "togetherCode": "1944675662079827968", "name": "234234", "rtCode": "*********6720690176", "rtName": "沉浸式观影大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-14 16:29:43", "checkOutTime": "2025-07-22 11:20:08", "payTime": "2025-07-22 11:20:08", "payBizDate": "2025-07-22", "payType": "房间账", "consSubFee": 2802.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 2802.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 2802.0, "payTotalFee": 2802.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "231", "gSrc": "member", "gSrcName": "会员"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1934448972967796736", "url": "/#/front/order/order_details?fullscreen=true&no=1934448972967796736&noType=order&modelValue=true&tabName=account", "togetherCode": "1934448973462724608", "name": "234234", "rtCode": "1831598419748958208", "rtName": "悦致城景大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-06-16 11:12:30", "checkOutTime": "2025-07-22 14:49:42", "payTime": "2025-07-22 14:49:42", "payBizDate": "2025-07-22", "payType": "房间账", "consSubFee": 31968.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 31968.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 31968.0, "payTotalFee": 31968.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "A06", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": null, "url": "", "togetherCode": "1947555433746866176", "name": "现付账-null", "rtCode": null, "rtName": "", "inType": null, "inTypeName": null, "checkInTime": null, "checkOutTime": null, "payTime": null, "payBizDate": "2025-07-22", "payType": "现付账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 222.0, "consumeTotalFee": 222.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 222.0, "payTotalFee": 222.0, "merge": 0.0, "payOperator": null, "payOperatorName": null, "payAccountList": null, "consumeAccountList": null, "rno": null, "gSrc": null, "gSrcName": null}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": null, "url": "", "togetherCode": "1947549376853086208", "name": "现付账-null", "rtCode": null, "rtName": "", "inType": null, "inTypeName": null, "checkInTime": null, "checkOutTime": null, "payTime": null, "payBizDate": "2025-07-22", "payType": "现付账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 357.0, "consumeTotalFee": 357.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 357.0, "payTotalFee": 357.0, "merge": 0.0, "payOperator": null, "payOperatorName": null, "payAccountList": null, "consumeAccountList": null, "rno": null, "gSrc": null, "gSrcName": null}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1945743461745221633", "url": "/#/front/order/order_details?fullscreen=true&no=1945743461745221633&noType=order&modelValue=true&tabName=account", "togetherCode": "1945743461782970368", "name": "订单蚁来哈哈", "rtCode": "*********6720690176", "rtName": "沉浸式观影大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-17 15:12:46", "checkOutTime": "2025-07-22 11:26:05", "payTime": "2025-07-22 11:26:05", "payBizDate": "2025-07-22", "payType": "房间账", "consSubFee": 1445.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 1445.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 1445.0, "payTotalFee": 1445.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "B02", "gSrc": "member", "gSrcName": "会员"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1947549078206058497", "url": "/#/front/order/order_details?fullscreen=true&no=1947549078206058497&noType=order&modelValue=true&tabName=account", "togetherCode": "1947549078315110400", "name": "234234", "rtCode": "*********6720690176", "rtName": "沉浸式观影大床房", "inType": "hour_room", "inTypeName": "钟点房", "checkInTime": "2025-07-22 14:47:39", "checkOutTime": "2025-07-22 14:48:25", "payTime": "2025-07-22 14:48:25", "payBizDate": "2025-07-22", "payType": "房间账", "consSubFee": 29.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 29.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 29.0, "payTotalFee": 29.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "B11", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1947463322389311489", "url": "/#/front/order/order_details?fullscreen=true&no=1947463322389311489&noType=order&modelValue=true&tabName=account", "togetherCode": "1947463384108494848", "name": "啊啊", "rtCode": "1820341977486307328", "rtName": "欢乐之家亲子双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-22 09:07:08", "checkOutTime": "2025-07-22 10:36:36", "payTime": "2025-07-22 10:36:36", "payBizDate": "2025-07-22", "payType": "房间账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 0.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 0.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "102", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1940714304648892417", "url": "/#/front/order/order_details?fullscreen=true&no=1940714304648892417&noType=order&modelValue=true&tabName=account", "togetherCode": "1940715517666775040", "name": "1", "rtCode": "186857*************", "rtName": "嗨致静谧双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-03 18:13:31", "checkOutTime": "2025-07-05 15:53:14", "payTime": "2025-07-05 15:53:14", "payBizDate": "2025-07-05", "payType": "房间账", "consSubFee": 444.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 444.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 444.0, "merge": 444.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "F01", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1940714304648892416", "url": "/#/front/order/order_details?fullscreen=true&no=1940714304648892416&noType=order&modelValue=true&tabName=account", "togetherCode": "1940715406958120960", "name": "1", "rtCode": "186857*************", "rtName": "嗨致静谧双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-03 18:13:04", "checkOutTime": "2025-07-05 15:53:14", "payTime": "2025-07-05 15:53:14", "payBizDate": "2025-07-05", "payType": "房间账", "consSubFee": 444.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 444.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 1776.0, "payTotalFee": 444.0, "merge": -1332.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "4511", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1940714304648892418", "url": "/#/front/order/order_details?fullscreen=true&no=1940714304648892418&noType=order&modelValue=true&tabName=account", "togetherCode": "1940715557143564288", "name": "2", "rtCode": "186857*************", "rtName": "嗨致静谧双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-03 18:13:40", "checkOutTime": "2025-07-05 15:53:14", "payTime": "2025-07-05 15:53:14", "payBizDate": "2025-07-05", "payType": "房间账", "consSubFee": 444.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 444.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 444.0, "merge": 444.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "F02", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1940714304653086721", "url": "/#/front/order/order_details?fullscreen=true&no=1940714304653086721&noType=order&modelValue=true&tabName=account", "togetherCode": "1940715692581834752", "name": "6", "rtCode": "186857*************", "rtName": "嗨致静谧双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-03 18:14:12", "checkOutTime": "2025-07-05 15:53:14", "payTime": "2025-07-05 15:53:14", "payBizDate": "2025-07-05", "payType": "房间账", "consSubFee": 444.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 444.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 444.0, "merge": 444.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "4515", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1941403879172067328", "url": "/#/front/order/order_details?fullscreen=true&no=1941403879172067328&noType=order&modelValue=true&tabName=account", "togetherCode": "1941405155100315648", "name": "测试", "rtCode": "186857*************", "rtName": "嗨致静谧双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-05 15:53:53", "checkOutTime": "2025-07-05 15:56:21", "payTime": "2025-07-05 15:56:21", "payBizDate": "2025-07-05", "payType": "房间账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 0.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 0.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "4511", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1934807774966427649", "url": "/#/front/order/order_details?fullscreen=true&no=1934807774966427649&noType=order&modelValue=true&tabName=account", "togetherCode": "1934807775054508032", "name": "青蛙", "rtCode": "*********6720690176", "rtName": "沉浸式观影大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-06-17 10:58:15", "checkOutTime": "2025-07-21 11:28:06", "payTime": "2025-07-21 11:28:06", "payBizDate": "2025-07-21", "payType": "房间账", "consSubFee": 10624.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 10624.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 10624.0, "payTotalFee": 10624.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "242", "gSrc": "member", "gSrcName": "会员"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": null, "url": "", "togetherCode": "2", "name": "现付账-会员充值", "rtCode": null, "rtName": "", "inType": null, "inTypeName": null, "checkInTime": null, "checkOutTime": null, "payTime": null, "payBizDate": "2025-07-21", "payType": "现付账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 4000.0, "consumeTotalFee": 4000.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 4000.0, "payTotalFee": 4000.0, "merge": 0.0, "payOperator": null, "payOperatorName": null, "payAccountList": null, "consumeAccountList": null, "rno": null, "gSrc": null, "gSrcName": null}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1934857429667610625", "url": "/#/front/order/order_details?fullscreen=true&no=1934857429667610625&noType=order&modelValue=true&tabName=account", "togetherCode": "1934857429705359360", "name": "第一个", "rtCode": "*********6720690176", "rtName": "沉浸式观影大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-06-17 14:15:34", "checkOutTime": "2025-07-21 15:24:29", "payTime": "2025-07-21 15:24:29", "payBizDate": "2025-07-21", "payType": "房间账", "consSubFee": 11288.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 11288.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 11288.0, "payTotalFee": 11288.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "246", "gSrc": "member", "gSrcName": "会员"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1947194909083365377", "url": "/#/front/order/order_details?fullscreen=true&no=1947194909083365377&noType=order&modelValue=true&tabName=account", "togetherCode": "1947194909586681856", "name": "好好", "rtCode": "*********6720690176", "rtName": "沉浸式观影大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-21 15:20:18", "checkOutTime": "2025-07-21 15:20:26", "payTime": "2025-07-21 15:20:26", "payBizDate": "2025-07-21", "payType": "房间账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 0.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 0.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "ABC001", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1945716973721411585", "url": "/#/front/order/order_details?fullscreen=true&no=1945716973721411585&noType=order&modelValue=true&tabName=account", "togetherCode": "1945716973813686272", "name": "234234", "rtCode": "1812733505999212544", "rtName": "商旅如意双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-17 13:27:31", "checkOutTime": "2025-07-21 10:30:08", "payTime": "2025-07-21 10:30:08", "payBizDate": "2025-07-21", "payType": "房间账", "consSubFee": 868.44, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 868.44, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 868.44, "payTotalFee": 868.44, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "uu10", "gSrc": "member", "gSrcName": "会员"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1938523016423563265", "url": "/#/front/order/order_details?fullscreen=true&no=1938523016423563265&noType=order&modelValue=true&tabName=account", "togetherCode": "1938523032659714048", "name": "1231", "rtCode": "1868570661209554944", "rtName": "商旅如意影音大床房", "inType": "travel_group", "inTypeName": "旅行团", "checkInTime": "2025-06-27 17:01:49", "checkOutTime": "2025-07-01 11:58:07", "payTime": "2025-07-01 11:58:07", "payBizDate": "2025-07-01", "payType": "房间账", "consSubFee": 444.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 444.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 444.0, "payTotalFee": 444.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "1021", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1938474974140235776", "url": "/#/front/order/order_details?fullscreen=true&no=1938474974140235776&noType=order&modelValue=true&tabName=account", "togetherCode": "1938477010940407808", "name": "5", "rtCode": "186857*************", "rtName": "嗨致静谧双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-06-27 13:58:29", "checkOutTime": "2025-07-01 11:57:46", "payTime": "2025-07-01 11:57:46", "payBizDate": "2025-07-01", "payType": "房间账", "consSubFee": 888.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 888.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 888.0, "payTotalFee": 888.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "4512", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1938474974140235777", "url": "/#/front/order/order_details?fullscreen=true&no=1938474974140235777&noType=order&modelValue=true&tabName=account", "togetherCode": "1938477057312632832", "name": "推广推广", "rtCode": "186857*************", "rtName": "嗨致静谧双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-06-27 13:58:40", "checkOutTime": "2025-07-01 11:57:55", "payTime": "2025-07-01 11:57:55", "payBizDate": "2025-07-01", "payType": "房间账", "consSubFee": 888.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 888.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 888.0, "payTotalFee": 888.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "4514", "gSrc": "walk_in", "gSrcName": "散客"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": null, "url": "", "togetherCode": "2", "name": "现付账-会员充值", "rtCode": null, "rtName": "", "inType": null, "inTypeName": null, "checkInTime": null, "checkOutTime": null, "payTime": null, "payBizDate": "2025-07-17", "payType": "现付账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 3000.0, "consumeTotalFee": 3000.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 3000.0, "payTotalFee": 3000.0, "merge": 0.0, "payOperator": null, "payOperatorName": null, "payAccountList": null, "consumeAccountList": null, "rno": null, "gSrc": null, "gSrcName": null}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1941407895977316352", "url": "/#/front/order/order_details?fullscreen=true&no=1941407895977316352&noType=order&modelValue=true&tabName=account", "togetherCode": "1941408009122861056", "name": "冲冲冲", "rtCode": "1831598419748958208", "rtName": "悦致城景大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-05 16:05:13", "checkOutTime": "2025-07-17 10:27:00", "payTime": "2025-07-17 10:27:00", "payBizDate": "2025-07-17", "payType": "房间账", "consSubFee": 5330.0, "couponDeduction": -20.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 5310.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 2.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 5308.0, "payTotalFee": 5310.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "ADS01", "gSrc": "member", "gSrcName": "会员"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1938163155374768128", "url": "/#/front/order/order_details?fullscreen=true&no=1938163155374768128&noType=order&modelValue=true&tabName=account", "togetherCode": "1938163201340145664", "name": "*********", "rtCode": "*********6720690176", "rtName": "沉浸式观影大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-06-26 17:11:31", "checkOutTime": "2025-07-17 15:13:40", "payTime": "2025-07-17 15:13:40", "payBizDate": "2025-07-17", "payType": "房间账", "consSubFee": 6972.0, "couponDeduction": -332.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 6640.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 6640.0, "payTotalFee": 6640.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "B07", "gSrc": "member", "gSrcName": "会员"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1938060426991947776", "url": "/#/front/order/order_details?fullscreen=true&no=1938060426991947776&noType=order&modelValue=true&tabName=account", "togetherCode": "1938061552474705920", "name": "还好", "rtCode": "1820341977486307328", "rtName": "欢乐之家亲子双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-06-26 10:27:36", "checkOutTime": "2025-07-17 15:47:28", "payTime": "2025-07-17 15:47:28", "payBizDate": "2025-07-17", "payType": "房间账", "consSubFee": 9324.0, "couponDeduction": -444.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 8880.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 8880.0, "payTotalFee": 8880.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "310", "gSrc": "member", "gSrcName": "会员"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1934803251355037696", "url": "/#/front/order/order_details?fullscreen=true&no=1934803251355037696&noType=order&modelValue=true&tabName=account", "togetherCode": "1934803251627667456", "name": "12", "rtCode": "*********6720690176", "rtName": "沉浸式观影大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-06-17 10:40:17", "checkOutTime": "2025-07-17 16:47:56", "payTime": "2025-07-17 16:47:56", "payBizDate": "2025-07-17", "payType": "房间账", "consSubFee": 9960.0, "couponDeduction": -664.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 9296.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 9296.0, "payTotalFee": 9296.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "233", "gSrc": "member", "gSrcName": "会员"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": null, "url": "", "togetherCode": "2", "name": "现付账-会员充值", "rtCode": null, "rtName": "", "inType": null, "inTypeName": null, "checkInTime": null, "checkOutTime": null, "payTime": null, "payBizDate": "2025-07-16", "payType": "现付账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 2012.0, "consumeTotalFee": 2012.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 2012.0, "payTotalFee": 2012.0, "merge": 0.0, "payOperator": null, "payOperatorName": null, "payAccountList": null, "consumeAccountList": null, "rno": null, "gSrc": null, "gSrcName": null}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1932024591222120449", "url": "/#/front/order/order_details?fullscreen=true&no=1932024591222120449&noType=order&modelValue=true&tabName=account", "togetherCode": "1932024591297617920", "name": "123111", "rtCode": "186857*************", "rtName": "嗨致静谧双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-06-09 18:38:52", "checkOutTime": "2025-07-16 09:31:44", "payTime": "2025-07-16 09:31:44", "payBizDate": "2025-07-16", "payType": "房间账", "consSubFee": 7252.0, "couponDeduction": 196.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 7448.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 13.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 7043.0, "payTotalFee": 7056.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "4505", "gSrc": "member", "gSrcName": "会员"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1945025142075437056", "url": "/#/front/order/order_details?fullscreen=true&no=1945025142075437056&noType=order&modelValue=true&tabName=account", "togetherCode": "1945025142192877568", "name": "测一测计费", "rtCode": "*********6720690176", "rtName": "沉浸式观影大床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-15 15:38:25", "checkOutTime": "2025-07-16 14:45:56", "payTime": "2025-07-16 14:45:56", "payBizDate": "2025-07-16", "payType": "房间账", "consSubFee": 664.0, "couponDeduction": 332.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 996.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 332.0, "payTotalFee": 332.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "B09", "gSrc": "member", "gSrcName": "会员"}, {"id": null, "gcode": "1812687065731567616", "hcode": "1812687588224405504", "orderNo": "1945372556787499008", "url": "/#/front/order/order_details?fullscreen=true&no=1945372556787499008&noType=order&modelValue=true&tabName=account", "togetherCode": "1945372556938493952", "name": "张三", "rtCode": "1812733505999212544", "rtName": "商旅如意双床房", "inType": "all_day", "inTypeName": "全天房", "checkInTime": "2025-07-16 14:38:55", "checkOutTime": "2025-07-16 14:39:10", "payTime": "2025-07-16 14:39:10", "payBizDate": "2025-07-16", "payType": "房间账", "consSubFee": 0.0, "couponDeduction": 0.0, "consSubCate": 0.0, "consSubGoods": 0.0, "consSubCard": 0.0, "consSubMeetroom": 0.0, "consSubOth": 0.0, "consumeTotalFee": 0.0, "paySubCash": 0.0, "paySubDeposit": 0.0, "paySubCashBack": 0.0, "paySubBank": 0.0, "paySubStoreCard": 0.0, "paySubAr": 0.0, "paySubWx": 0.0, "paySubAlpay": 0.0, "paySubCredit": 0.0, "paySubOth": 0.0, "payTotalFee": 0.0, "merge": 0.0, "payOperator": "***********", "payOperatorName": "一朵花", "payAccountList": null, "consumeAccountList": null, "rno": "uu05", "gSrc": "member", "gSrcName": "会员"}], "consumeNameList": null, "payNameList": null}, "msg": ""}