<i18n>
{
  "zh": {
    "search": {
      "store": "门店",
      "selectStore": "请选择门店",
      "bizDate": "营业日",
      "selectBizDate": "选择日期",
      "guestSource": "客源",
      "selectGuestSource": "请选择客源",
      "memberLevel": "会员等级",
      "selectLevel": "请选择级别",
      "agentCompany": "中介公司",
      "selectAgentCompany": "请选择中介公司",
      "protocolCompany": "协议公司",
      "selectProtocolCompany": "请选择协议公司",
      "roomType": "房型",
      "selectRoomType": "请选择房型",
      "orderSource": "订单来源",
      "selectOrderSource": "请选择订单来源",
      "checkinType": "入住类型",
      "selectCheckinType": "请选择入住类型",
      "statChannel": "统计渠道",
      "selectStatChannel": "请选择统计渠道",
      "selectOperator": "请选择",
      "query": "查询",
      "today": "今天",
      "thisWeek": "本周",
      "thisMonth": "本月",
      "operationTime": "操作时间",
      "startDate": "开始日期",
      "endDate": "结束日期",
      "to": "至",
      "oldRNo": "旧房号",
      "selectOldRNo": "请输入旧房号",
      "newRNo": "新房号",
      "selectNewRNo": "请输入新房号",
      "changeRoomType": "换房类型",
      "selectChangeRoomType": "请选择换房类型",
      "freeUpgrade": "免费升级",
      "roomChange": "换房"
    }
  },
  "en": {
    "search": {
      "store": "Store",
      "selectStore": "Select Store",
      "bizDate": "Biz Date",
      "selectBizDate": "Select Date",
      "guestSource": "Source",
      "selectGuestSource": "Select Guest Source",
      "memberLevel": "Level",
      "selectLevel": "Select Member Level",
      "agentCompany": "Agent Company",
      "selectAgentCompany": "Select Agent Company",
      "protocolCompany": "Protocol Company",
      "selectProtocolCompany": "Select Protocol Company",
      "roomType": "Type",
      "selectRoomType": "Select Room Type",
      "orderSource": "Order",
      "selectOrderSource": "Select Order Source",
      "checkinType": "Check-in",
      "selectCheckinType": "Select Check-in Type",
      "statChannel": "Channel",
      "selectStatChannel": "Select Stat Channel",
      "selectOperator": "Select",
      "query": "Search",
      "today": "Today",
      "thisWeek": "This Week",
      "thisMonth": "This Month",
      "operationTime": "Operation Time",
      "startDate": "Start Date",
      "endDate": "End Date",
      "to": "to",
      "oldRNo": "Old Room No",
      "selectOldRNo": "Enter Old Room No",
      "newRNo": "New Room No",
      "selectNewRNo": "Enter New Room No",
      "changeRoomType": "Change Type",
      "selectChangeRoomType": "Select Change Type",
      "freeUpgrade": "Free Upgrade",
      "roomChange": "Room Change"
    }
  },
  "km": {
    "search": {
      "store": "ហាង",
      "selectStore": "សូមជ្រើសរើសហាង",
      "bizDate": "កាលបរិច្ឆេទអាជីវកម្ម",
      "selectBizDate": "ជ្រើសរើសកាលបរិច្ឆេទ",
      "guestSource": "ប្រភពភ្ញៀវ",
      "selectGuestSource": "សូមជ្រើសរើសប្រភពភ្ញៀវ",
      "memberLevel": "កម្រិតសមាជិក",
      "selectLevel": "សូមជ្រើសរើសកម្រិត",
      "agentCompany": "ក្រុមហ៊ុនអ្នកចាត់ការ",
      "selectAgentCompany": "សូមជ្រើសរើសក្រុមហ៊ុនអ្នកចាត់ការ",
      "protocolCompany": "ក្រុមហ៊ុនព្រមាន",
      "selectProtocolCompany": "សូមជ្រើសរើសក្រុមហ៊ុនព្រមាន",
      "roomType": "ប្រភេទបន្ទប់",
      "selectRoomType": "សូមជ្រើសរើសប្រភេទបន្ទប់",
      "orderSource": "ប្រភពការបញ្ជាទិញ",
      "selectOrderSource": "សូមជ្រើសរើសប្រភពការបញ្ជាទិញ",
      "checkinType": "ប្រភេទចូលស្នាក់នៅ",
      "selectCheckinType": "សូមជ្រើសរើសប្រភេទចូលស្នាក់នៅ",
      "statChannel": "ឆានែលស្ថិតិ",
      "selectStatChannel": "សូមជ្រើសរើសឆានែលស្ថិតិ",
      "selectOperator": "សូមជ្រើសរើស",
      "query": "ស្វែងរក",
      "today": "ថ្ងៃនេះ",
      "thisWeek": "សប្តាហ៍នេះ",
      "thisMonth": "ខែនេះ",
      "operationTime": "ពេលវេលាប្រតិបត្តិការ",
      "startDate": "កាលបរិច្ឆេទចាប់ផ្តើម",
      "endDate": "កាលបរិច្ឆេទបញ្ចប់",
      "to": "ទៅ",
      "oldRNo": "លេខបន្ទប់ចាស់",
      "selectOldRNo": "បញ្ចូលលេខបន្ទប់ចាស់",
      "newRNo": "លេខបន្ទប់ថ្មី",
      "selectNewRNo": "បញ្ចូលលេខបន្ទប់ថ្មី",
      "changeRoomType": "ប្រភេទប្តូរបន្ទប់",
      "selectChangeRoomType": "ជ្រើសរើសប្រភេទប្តូរបន្ទប់",
      "freeUpgrade": "ដំឡើងកម្រិតដោយឥតគិតថ្លៃ",
      "roomChange": "ប្តូរបន្ទប់"
    }
  }
}
</i18n>

<script setup lang="ts">
import { dayjs } from 'element-plus'
import { useI18n } from 'vue-i18n'
import {
  getRoomChangeUpgradeReportList,
  managerMonthReportApi,
  simpleListApi,
} from '@/api/modules/pms/shiftTimeApi.ts'
import useUserStore from '@/store/modules/user.ts'
import { getFirstDayOfMonth, getMonday } from '@/utils/tool'
import { ReportUtils } from '@/utils/report-utils.ts'

const userStore = useUserStore()
const { t } = useI18n()
const route = useRoute()

const data = ref({
  searchFold: false,
  // 表格是否自适应高度
  tableAutoHeight: true,
  labelState: '1',
  bizDate: [dayjs().subtract(30, 'day').toDate(), dayjs().subtract(0, 'day').toDate()] as any, // 起始时间为30天前，结束时间为前一天
  search: {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    statTypeList: [],
    oldRNo: '',
    newRNo: '',
    changeRoomType: '',
  },
})

const shortcuts = [
  {
    text: t('search.today'),
    value: () => {
      const end = new Date()
      const start = new Date()
      return [start, end]
    },
  },
  {
    text: t('search.thisWeek'),
    value: () => {
      const end = new Date()
      const start = getMonday()
      return [start, end]
    },
  },
  {
    text: t('search.thisMonth'),
    value: () => {
      const end = new Date()
      const start = getFirstDayOfMonth()
      return [start, end]
    },
  },
]

const simpleList = ref<{ hcode: string, hname: string }[]>([])

// 换房类型选项
const changeRoomTypeOptions = computed(() => [
  { value: 'free_upgrade', label: t('search.freeUpgrade') },
  { value: 'room_change', label: t('search.roomChange') }
])

const loading = ref(false)
function onSearch() {
  loading.value = true
  getRoomChangeUpgradeReportList({
    ...data.value.search,
    startDate: dayjs(data.value.bizDate[0]).format('YYYY-MM-DD'),
    endDate: dayjs(data.value.bizDate[1]).format('YYYY-MM-DD'),
  }).then((res) => {
    nextTick(() => {
      setJson(res)
      loading.value = false
    })
  }).catch(() => {
    loading.value = false
  })
}

async function setJson(json: any) {
  const viewer = ReportUtils.getViewer('roomChangeUpgradeStiViewer')
  const report = ReportUtils.getReportObj('reports/roomChangeUpgradeReport.mrt', json)

  ReportUtils.loadData(viewer, report, 'roomChangeUpgradeReport')
}

async function getsimpleList() {
  const params = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
  }
  await simpleListApi(params).then((res) => {
    simpleList.value = res.data
  })
}
async function getQuery() {
  const query = route.query
  if (!query || JSON.stringify(query) === '{}') {
    return
  }
  if (!query.name || query.name !== 'roomChangeUpgradeReport') {
    return
  }
  if (query && query.startDate && query.endDate) {
    // 将endDate转换为月份最后一天
    const lastDayOfMonth = dayjs(query.endDate).endOf('month').format('YYYY-MM-DD')
    data.value.bizDate = [query.startDate, lastDayOfMonth]
    data.value.search.hcode = query.hcode
  }
}

onMounted(async () => {
  await getQuery()
  await getsimpleList()
  onSearch()
})

// -----------最多只能选择一个月，且只能选昨天之前的-----------------
const startVal = ref(null)
/** 选择第一个时间的当月天数 */
const daysInMonth = ref(0)
function calendarChange(e) {
  if (e[0] && !e[1]) {
    startVal.value = e[0]
    daysInMonth.value = dayjs(e[0]).daysInMonth()
  }
}
/** 选择第一个时间 */
function visibleChange(e) {
  if (e) {
    startVal.value = null
  }
}
/** 禁止选择一个上下自然月之外时间 */
function disabledDate(time) {
  const flag_yestodayAfter = dayjs(time).isAfter(dayjs().subtract(1, 'day'), 'day')
  if (!startVal.value) {
    return flag_yestodayAfter
  }
  const range = [dayjs(startVal.value).subtract(daysInMonth.value, 'day'), dayjs(startVal.value).add(daysInMonth.value, 'day')]
  const cur = dayjs(time)
  return cur < range[0] || cur > range[1] || flag_yestodayAfter
}
// -----------最多只能选择一个月，且只能选昨天之前的-----------------
</script>

<template>
  <div :class="{ 'absolute-container': data.tableAutoHeight }">
    <page-main>
      <search-bar :fold="data.searchFold" :show-toggle="false">
        <el-form :model="data.search" size="default" label-width="80px" inline-message inline class="search-form">
          <el-form-item :label="t('search.store')" style="width: 260px;">
            <el-select v-model="data.search.hcode" :placeholder="t('search.selectStore')">
              <el-option v-for="item in simpleList" :key="item.hcode" :label="item.hname" :value="item.hcode" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('search.bizDate')" style="width: 360px;">
            <el-date-picker
              v-model="data.bizDate"
              type="daterange"
              :range-separator="t('search.to')"
              :start-placeholder="t('search.startDate')"
              :end-placeholder="t('search.endDate')"
              :shortcuts="shortcuts"
              :clearable="false"
              :disabled-date="disabledDate"
              @calendar-change="calendarChange"
              @visible-change="visibleChange"
            />
          </el-form-item>
          <el-form-item :label="t('search.oldRNo')" style="width: 200px;">
            <el-input v-model="data.search.oldRNo" :placeholder="t('search.selectOldRNo')" clearable />
          </el-form-item>
          <el-form-item :label="t('search.newRNo')" style="width: 200px;">
            <el-input v-model="data.search.newRNo" :placeholder="t('search.selectNewRNo')" clearable />
          </el-form-item>
          <el-form-item :label="t('search.changeRoomType')" style="width: 200px;">
            <el-select v-model="data.search.changeRoomType" :placeholder="t('search.selectChangeRoomType')" clearable>
              <el-option v-for="item in changeRoomTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">
              {{ t('search.query') }}
            </el-button>
          </el-form-item>
        </el-form>
      </search-bar>
      <div id="roomChangeUpgradeReport">
        <el-skeleton :loading="loading" animated :rows="30" />
      </div>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-radio) {
  margin-right: 14px;
  overflow: scroll auto;
  white-space: nowrap;
}
</style>
